# Legacy Field Cleanup for Profile Model Architecture

This document outlines the cleanup process for removing the legacy `family_member_id` fields from the Skill and Experience models as part of the centralized Profile model architecture migration.

## Overview

The Skill and Experience models have been updated to use the centralized Profile model architecture where all user-related data (skills, experiences, achievements) connects to the shared Profile model rather than directly to FamilyMember models.

## Changes Made

### 1. Skill Model Updates (`app/Models/Skill.php`)

**Removed:**
- `family_member_id` from `$fillable` array
- `familyMember()` relationship method

**Kept:**
- `profile_id` field and `profile()` relationship
- All other existing functionality

### 2. Experience Model Updates (`app/Models/Experience.php`)

**Removed:**
- `family_member_id` from `$fillable` array
- `family_member()` relationship method

**Kept:**
- `profile_id` field and `profile()` relationship
- All other existing functionality (job_title, company_name, location, etc.)

### 3. FamilyMember Model Updates (`app/Models/FamilyMember.php`)

**Updated:**
- `skills()` method now returns empty collection when no profile exists instead of falling back to legacy relationship
- `experiences()` method now returns empty collection when no profile exists instead of falling back to legacy relationship

### 4. Skills Database Migration (`database/migrations/2025_05_28_194000_remove_family_member_id_from_skills_table.php`)

**Features:**
- Data integrity verification before column removal
- Drops foreign key constraints
- Removes `family_member_id` column from skills table
- Rollback support for emergency recovery

### 5. Experiences Database Migration (`database/migrations/2025_05_28_195000_remove_family_member_id_from_experiences_table.php`)

**Features:**
- Data integrity verification before column removal
- Drops foreign key constraints
- Removes `family_member_id` column from experiences table
- Rollback support for emergency recovery

### 6. Skills Verification Migration (`database/migrations/2025_05_28_194001_verify_skills_migration_status.php`)

**Provides:**
- Comprehensive migration status report for skills
- Counts of migrated, unmigrated, and orphaned skills
- Clear indicators for cleanup readiness

### 7. Experiences Verification Migration (`database/migrations/2025_05_28_195001_verify_experiences_migration_status.php`)

**Provides:**
- Comprehensive migration status report for experiences
- Counts of migrated, unmigrated, and orphaned experiences
- Clear indicators for cleanup readiness

## Migration Process

### Step 1: Verify Current Status

**For Skills:**
```bash
php artisan migrate --path=database/migrations/2025_05_28_194001_verify_skills_migration_status.php
```

**For Experiences:**
```bash
php artisan migrate --path=database/migrations/2025_05_28_195001_verify_experiences_migration_status.php
```

### Step 2: Ensure Data Migration is Complete
If verification shows unmigrated records, run:
```bash
php artisan migrate --path=database/migrations/2025_05_28_161733_migrate_existing_data_to_profiles.php
```

### Step 3: Remove Legacy Columns

**Remove Skills legacy column:**
```bash
php artisan migrate --path=database/migrations/2025_05_28_194000_remove_family_member_id_from_skills_table.php
```

**Remove Experiences legacy column:**
```bash
php artisan migrate --path=database/migrations/2025_05_28_195000_remove_family_member_id_from_experiences_table.php
```

## Data Integrity Checks

The cleanup migrations include automatic verification:

**For Skills:**
1. **Unmigrated Skills Check**: Ensures no skills have `family_member_id` without `profile_id`
2. **Orphaned Skills Check**: Identifies skills without any parent reference
3. **Migration Completeness**: Confirms all skills are properly linked to profiles

**For Experiences:**
1. **Unmigrated Experiences Check**: Ensures no experiences have `family_member_id` without `profile_id`
2. **Orphaned Experiences Check**: Identifies experiences without any parent reference
3. **Migration Completeness**: Confirms all experiences are properly linked to profiles

## Rollback Plan

If issues arise, the migrations can be rolled back:

**Rollback Skills cleanup:**
```bash
php artisan migrate:rollback --path=database/migrations/2025_05_28_194000_remove_family_member_id_from_skills_table.php
```

**Rollback Experiences cleanup:**
```bash
php artisan migrate:rollback --path=database/migrations/2025_05_28_195000_remove_family_member_id_from_experiences_table.php
```

This will restore the `family_member_id` columns, but data migration will need to be re-run.

## Testing Recommendations

After cleanup completion, verify:

1. **API Endpoints**: All skill and experience-related API endpoints work correctly
2. **Profile Relationships**: Skills and experiences are properly loaded through profile relationships
3. **Data Integrity**: No orphaned or missing skill/experience records
4. **Performance**: No N+1 query issues in skill/experience loading
5. **CRUD Operations**: Creating, reading, updating, and deleting skills/experiences works correctly

## Related Files

**Models:**
- `app/Models/Skill.php` - Updated model
- `app/Models/Experience.php` - Updated model
- `app/Models/FamilyMember.php` - Updated relationship delegation

**Controllers:**
- `app/Http/Controllers/Api/ProfileController.php` - Already updated to use Profile relationships

**Migrations:**
- `database/migrations/2025_05_28_194000_remove_family_member_id_from_skills_table.php` - Skills cleanup migration
- `database/migrations/2025_05_28_194001_verify_skills_migration_status.php` - Skills verification migration
- `database/migrations/2025_05_28_195000_remove_family_member_id_from_experiences_table.php` - Experiences cleanup migration
- `database/migrations/2025_05_28_195001_verify_experiences_migration_status.php` - Experiences verification migration

## Next Steps

This cleanup is part of a larger migration to centralized Profile model architecture. Similar cleanup should be performed for:

1. ✅ ~~Experience model (`family_member_id` removal)~~ - **COMPLETED**
2. Achievement model (`family_member_id` removal)
3. Review model (legacy field cleanup)
4. Comment model (legacy field cleanup)

## Success Criteria

**For Skills:**
✅ All skills have `profile_id` assigned
✅ No skills have only `family_member_id` without `profile_id`
✅ Legacy `family_member_id` column removed from skills table
✅ All skill-related API endpoints continue to work correctly
✅ No performance degradation in skill queries

**For Experiences:**
✅ All experiences have `profile_id` assigned
✅ No experiences have only `family_member_id` without `profile_id`
✅ Legacy `family_member_id` column removed from experiences table
✅ All experience-related API endpoints continue to work correctly
✅ No performance degradation in experience queries

**General:**
✅ Proper error handling for edge cases
✅ 100% backward compatibility maintained
✅ Profile relationships work correctly
